using RediSoftware.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using RediSoftware.Dtos;
using AutoMapper;
using RediSoftware.Redi_Utility;
using RediSoftware.Helpers;
using RediSoftware.App_Start;
using RediSoftware.Common;
using System.Globalization;
using AutoMapper.QueryableExtensions;
using Kendo.DynamicLinq;
using System.Configuration;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace RediSoftware.BusinessLogic
{
    /// <summary>
    /// Provides methods to update both SURFACES and OPENINGS.
    /// </summary>
    public class ServiceTemplate : BusinessLogicBase
    {
        private readonly SqlConnection _connection;

        public ServiceTemplate(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _mapper = mapper;
            _unitOfWork = unitOfWork;
            _connection = new SqlConnection(ConfigurationManager.ConnectionStrings["RediSoftware"].ConnectionString);
            _connection.Open();
        }

        private IQueryable<RSS_ServiceTemplateView> InnerGet()
        {
            _unitOfWork.ReadOnly();

            IQueryable<RSS_ServiceTemplateView> query = _unitOfWork.Context.RSS_ServiceTemplateView.Where(a => a.Deleted == false);

            return query;
        }

        // Adjust query given a set of multi-select filters
        private IQueryable<RSS_ServiceTemplateView> AlterQueryFromMultiFilters(IQueryable<RSS_ServiceTemplateView> query, ServiceFilterDataDto filterData)
        {
            IQueryable<RSS_ServiceTemplateView> newQuery = query;

            if (filterData.appliedFilters == null)
                return newQuery;

            foreach (var filterField in filterData.appliedFilters.Properties())
            {
                var fieldName = filterField.Name;
                var filterValues = filterField.Value.ToObject<List<string>>();

                // Skip if no values or if "Any" is selected (following Construction Database pattern)
                if (filterValues == null || !filterValues.Any() ||
                    filterValues.Any(x => x?.ToLower() == "any"))
                    continue;

                switch (fieldName)
                {
                    case "ServiceCategoryTitle":
                        if (filterValues.Any(x => x?.ToLower() == "not specified"))
                        {
                            var nonNullValues = filterValues.Where(x => x?.ToLower() != "not specified").ToList();
                            if (nonNullValues.Any())
                            {
                                newQuery = newQuery.Where(x => nonNullValues.Contains(x.ServiceCategoryTitle) || string.IsNullOrEmpty(x.ServiceCategoryTitle));
                            }
                            else
                            {
                                newQuery = newQuery.Where(x => string.IsNullOrEmpty(x.ServiceCategoryTitle));
                            }
                        }
                        else
                        {
                            newQuery = newQuery.Where(x => filterValues.Contains(x.ServiceCategoryTitle));
                        }
                        break;
                    case "ManufacturerDescription":
                        if (filterValues.Any(x => x?.ToLower() == "not specified"))
                        {
                            var nonNullValues = filterValues.Where(x => x?.ToLower() != "not specified").ToList();
                            if (nonNullValues.Any())
                            {
                                newQuery = newQuery.Where(x => nonNullValues.Contains(x.ManufacturerDescription) || string.IsNullOrEmpty(x.ManufacturerDescription));
                            }
                            else
                            {
                                newQuery = newQuery.Where(x => string.IsNullOrEmpty(x.ManufacturerDescription));
                            }
                        }
                        else
                        {
                            newQuery = newQuery.Where(x => filterValues.Contains(x.ManufacturerDescription));
                        }
                        break;
                }
            }

            return newQuery;
        }

        public ServiceTemplateDto Get(Guid ServiceTemplateId)
        {
            _unitOfWork.ReadOnly();

            var dbModel = _unitOfWork.Context.RSS_ServiceTemplate.Find(ServiceTemplateId);
            var dto = _mapper.Map<ServiceTemplateDto>(dbModel);
            return dto;
        }

        /// <summary>
        /// Returns a summarized view of our Service Templates.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public DataSourceResult<RSS_ServiceTemplateView> GetList(
            string fromDate = null,
            string toDate = null,
            bool? isDeleted = false,
            PagingParameters paging = null)
        {
            DateTime dtFromDate = DateTime.MinValue;
            DateTime dtToDate = DateTime.MaxValue;
            if (fromDate != null)
                dtFromDate = DateTime.Parse(fromDate, null, DateTimeStyles.RoundtripKind).ToLocalTime();
            if (toDate != null)
                dtToDate = DateTime.Parse(toDate, null, DateTimeStyles.RoundtripKind).ToLocalTime();

            paging.SetDefaultSort("Description");
            _unitOfWork.ReadOnly();

            var templates = _unitOfWork.Context.RSS_ServiceTemplateView
                    .Where(a => (a.CreatedOn >= dtFromDate && a.CreatedOn <= dtToDate) && (a.Deleted == isDeleted))
                    .ToDataSourceResult(paging.PageSize, paging.Skip, paging.Sort, paging.Filter, paging.Aggregate, paging.Export);

            return templates;
        }

        public List<ServiceTemplateDto> GetAll()
        {
            return _unitOfWork.Context.RSS_ServiceTemplate
                .Where(x => x.Deleted == false)
                .OrderBy(x => x.Description)
                .ProjectTo<ServiceTemplateDto>(_mapper.ConfigurationProvider)
                .ToList();
        }

        public Guid Create(ServiceTemplateDto serviceTemplateDto)
        {
            serviceTemplateDto.CreatedOn = DateTime.Now;
            serviceTemplateDto.CreatedByName = UtilityFunctions.CurrentUserName ?? "unknown";
            serviceTemplateDto.ServiceTemplateId = Guid.NewGuid();
            RSS_ServiceTemplate _dbModel = _mapper.Map<RSS_ServiceTemplate>(serviceTemplateDto);

            _unitOfWork.Context.RSS_ServiceTemplate.Add(_dbModel);
            _unitOfWork.Commit();

            // Clear cache when Service Database records are created
            ClearServiceFilterCache();

            this.RecordAdminEvent(AdminChangeType.Add, serviceTemplateDto.Description, "Added new ServiceTemplate.");
            _unitOfWork.Commit();
            return _dbModel.ServiceTemplateId;
        }

        public Guid Update(ServiceTemplateDto serviceTemplateDto)
        {
            serviceTemplateDto.ModifiedOn = DateTime.Now;
            serviceTemplateDto.ModifiedByName = UtilityFunctions.CurrentUserName ?? "unknown";

            var dbModel = _unitOfWork.Context.RSS_ServiceTemplate.Find(serviceTemplateDto.ServiceTemplateId);

            _mapper.Map(serviceTemplateDto, dbModel);
            _unitOfWork.Commit();

            // Clear cache when Service Database records are updated
            ClearServiceFilterCache();

            this.RecordAdminEvent(AdminChangeType.Update, serviceTemplateDto.Description, "Updated ServiceTemplate.");
            return dbModel.ServiceTemplateId;
        }

        public void Delete(Guid id)
        {
            var dbModel = _unitOfWork.Context.RSS_ServiceTemplate.Find(id);
            dbModel.ModifiedOn = DateTime.Now;
            dbModel.ModifiedByName = UtilityFunctions.CurrentUserName ?? "unknown";
            dbModel.Deleted = true;
            this.RecordAdminEvent(AdminChangeType.Delete, dbModel.Description, "");
            _unitOfWork.Commit();

            // Clear cache when Service Database records are deleted
            ClearServiceFilterCache();
        }

        public void UndoDelete(Guid id)
        {

            var dbModel = _unitOfWork.Context.RSS_ServiceTemplate.Find(id);
            dbModel.ModifiedOn = DateTime.Now;
            dbModel.ModifiedByName = UtilityFunctions.CurrentUserName ?? "unknown";
            dbModel.Deleted = false;
            this.RecordAdminEvent(AdminChangeType.Delete, dbModel.Description, "");
            _unitOfWork.Commit();

            // Clear cache when Service Database records are undeleted
            ClearServiceFilterCache();
        }

        public Guid Copy(Guid id)
        {
            var model = Get(id);
            model.ServiceTemplateId = Guid.NewGuid();
            model.CreatedOn = DateTime.Now;
            model.CreatedByName = UtilityFunctions.CurrentUserName ?? "unknown";
            model.ModifiedOn = null;
            model.ModifiedByName = null;
            model.Deleted = false;
            model.Description = model.Description + " Copy";

            var mapped = _mapper.Map<RSS_ServiceTemplate>(model);
            _unitOfWork.Context.RSS_ServiceTemplate.Add(mapped);

            this.RecordAdminEvent(AdminChangeType.Delete, model.Description, "");
            _unitOfWork.Commit();

            // Clear cache when Service Database records are copied
            ClearServiceFilterCache();

            return model.ServiceTemplateId;
        }


        public List<ServiceCategoryDto> GetServiceCategories()
        {
            var list = _unitOfWork.Context.RSS_ServiceCategory
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<ServiceCategoryDto>(_mapper.ConfigurationProvider)
                .ToList();

            return list;
        }


        public static Dictionary<string, bool> AssignDefaultColumnVisibility(string serviceCategoryCode)
        {
            // Used for intial column show/hide state.
            // Properties not in this list will be shown.

            var defaultHiddenColumns = new Dictionary<string, bool>();

            // Currently no properties are hidden by default for services. Leaving here as a stub.
            // Refer to Construction.AssignDefaultColumnVisibility for an example.

            return defaultHiddenColumns;
        }

        public void SetIsFavourite(Guid serviceTemplateId, bool isFavourite)
        {
            var dbModel = _unitOfWork.Context.RSS_ServiceTemplate.Find(serviceTemplateId);

            if (dbModel == null)
            {
                throw new Exception(string.Format("{1} row not found for Id: {0}", serviceTemplateId, "RSS_ServiceTemplate"));
            }

            // Update the database model
            dbModel.ModifiedOn = DateTime.Now;
            dbModel.ModifiedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
            dbModel.IsFavourite = isFavourite;

            _unitOfWork.Commit();

            // Clear cache when Service Database records are updated
            ClearServiceFilterCache();
        }

        public DataSourceResult<RSS_ServiceTemplateView> GetMultiFiltered(ServiceFilterDataDto filterData)
        {
            var query = InnerGet();

            if (query == null)
                return new DataSourceResult<RSS_ServiceTemplateView> { Data = new List<RSS_ServiceTemplateView>() };

            if (filterData.appliedFilters != null)
                query = AlterQueryFromMultiFilters(query, filterData);

            var results = query
                    .ToDataSourceResult(filterData.paging.PageSize, filterData.paging.Skip, filterData.paging.Sort ?? new List<string>() { "CreatedOn@@desc" }, filterData.paging.Filter, filterData.paging.Aggregate, filterData.paging.Export);

            return results;
        }

        public async Task<Dictionary<string, List<FilterOption>>> GetMultiFilterOptions(List<MultiFilterFieldDto> fieldsList)
        {
            // Generate a cache key based on the fields list
            string fieldsHash = string.Join(",", fieldsList.Select(f => f.field)).GetHashCode().ToString("X");
            string cacheKey = $"ServiceMultiFilterOptions_{fieldsHash}";

            // Check for bypass cache parameter
            bool bypasseCache = SystemParameter.GetIntParm("BypassMultiFilterOptionsCacheAdmin") == 1;

            Dictionary<string, List<FilterOption>> filterOptions = null;

            if (!bypasseCache)
            {
                // Try to get from cache first
                filterOptions = (Dictionary<string, List<FilterOption>>)CacheHandler.LookupCache(
                    CacheHandler.DataType_MultiFilterOptions,
                    cacheKey);

                if (filterOptions != null)
                {
                    return filterOptions;
                }
            }

            try
            {
                // Filter data to be returned
                filterOptions = new Dictionary<string, List<FilterOption>>();

                // Get unique values for each field
                string sql = $@"
                    SELECT
                        {string.Join(",", fieldsList.Select((field => {
                            return $@"
                            (
                                SELECT STRING_AGG(
                                    CAST(
                                        (CASE WHEN CAST([{field.field}] AS NVARCHAR(MAX)) IS NULL
                                            THEN 'Not Specified'
                                            ELSE CAST([{field.field}] AS NVARCHAR(MAX))
                                            END)
                                        AS NVARCHAR(MAX)
                                    ),
                                    ','
                                ) FROM (
                                    SELECT DISTINCT [{field.field}] FROM [dbo].[RSS_ServiceTemplateView]
                                    WHERE [Deleted] = 0
                                    ORDER BY [{field.field}] OFFSET 0 ROWS
                                ) [uniqueResults]
                            ) {field.field}";
                        })
                        ))}
                ";
                var cmd = new SqlCommand(sql, _connection);

                using (var sqlReader = await cmd.ExecuteReaderAsync())
                {
                    sqlReader.Read();
                    // Add each field result to the "filterOptions" dictionary, also with "Any" added
                    foreach (MultiFilterFieldDto field in fieldsList)
                    {
                        try
                        {
                            string thisResult = sqlReader[field.field] as string;
                            var options = new List<FilterOption>() { new FilterOption { name = "Any", value = "Any" } };

                            if (!string.IsNullOrEmpty(thisResult))
                            {
                                var values = thisResult.Split(',').Where(v => !string.IsNullOrEmpty(v)).ToList();

                                // Add "Not Specified" option first if there are null values
                                if (values.Contains("Not Specified"))
                                {
                                    options.Add(new FilterOption { name = "Not Specified", value = "Not Specified" });
                                    values = values.Where(v => v != "Not Specified").ToList();
                                }

                                // Add other values in alphabetical order
                                options.AddRange(values.OrderBy(v => v).Select(v => new FilterOption { name = v, value = v }));
                            }

                            filterOptions.Add(field.field, options);
                        }
                        catch (Exception)
                        {
                            filterOptions.Add(
                                field.field,
                                new List<FilterOption>() { new FilterOption { name = "Any", value = "Any" } }
                            );
                        }
                    }
                    sqlReader.Close();
                }

                // Save to cache
                CacheHandler.SaveToCache(CacheHandler.DataType_MultiFilterOptions, cacheKey, filterOptions);

                // Make sure next call uses cache
                if (bypasseCache)
                {
                    SystemParameter.UpdateIntParm("BypassMultiFilterOptionsCacheAdmin", 0);
                }

                return filterOptions;
            }
            catch (Exception e)
            {
                throw e;
            }
        }

        public async Task<Dictionary<string, Dictionary<string, int>>> GetFilterCountData(ServiceFilterDataDto filterData)
        {
            // Check for bypass cache parameter
            bool bypasseCache = SystemParameter.GetIntParm("BypassFilterCountDataCacheAdmin") == 1;

            // Current filters applied
            bool anyFiltersApplied = false;
            Dictionary<string, List<string>> currentFilters = new Dictionary<string, List<string>>();

            if (filterData.fields != null && filterData.appliedFilters != null)
            {
                foreach (MultiFilterFieldDto field in filterData.fields.Where(f => !f.isDate))
                {
                    if (filterData.appliedFilters.ContainsKey(field.field))
                    {
                        List<string> fieldSelections = JsonConvert.DeserializeObject<List<string>>(filterData.appliedFilters[field.field].ToString());
                        bool hasFilter = fieldSelections.Where(s => s.ToString()?.ToLower() == "any").Count() == 0;
                        currentFilters[field.field] = hasFilter ? fieldSelections : null;
                        if (!anyFiltersApplied && hasFilter) { anyFiltersApplied = true; }
                    }
                }
            }

            // Generate cache key
            string cacheKey = GenerateServiceFilterCacheKey(filterData, anyFiltersApplied);

            if (!bypasseCache)
            {
                // Try to get from cache first
                var cachedResult = (Dictionary<string, Dictionary<string, int>>)CacheHandler.LookupCache(
                    CacheHandler.DataType_MultiFilterCountData,
                    cacheKey);

                if (cachedResult != null)
                {
                    return cachedResult;
                }
            }

            // Resulting filter count data
            Dictionary<string, Dictionary<string, int>> filterCountData = new Dictionary<string, Dictionary<string, int>>();

            // Build sql condition for current filters
            List<FieldSqlCondition> currentFiltersSqlConditions = GetSqlConditions();

            // FOR EACH field
            foreach (MultiFilterFieldDto field in filterData.fields)
            {
                string fieldName = field.field;
                string fieldCondition = null;
                string fieldAppliedFilters = filterData.appliedFilters[field.field].ToString();

                // Normal field (no date fields for service templates)
                if (currentFilters[fieldName] != null && currentFilters[fieldName]?.Where(s => s.ToString()?.ToLower() != "not specified").Count() > 0)
                {
                    fieldCondition = $"[{fieldName}] IN ({string.Join(",", currentFilters[fieldName].Where(s => s.ToString()?.ToLower() != "not specified").Select(s => field.isDecimal ? $"{s}" : $"'{s}'"))})";
                    if (currentFilters[fieldName].Select(s => s.ToLower()).Contains("not specified"))
                    {
                        fieldCondition = $"({fieldCondition} OR [{fieldName}] IS NULL)";
                    }
                }
                else if (currentFilters[fieldName]?.Where(s => s.ToString()?.ToLower() == "not specified").Count() > 0)
                {
                    fieldCondition = $"[{fieldName}] IS NULL";
                }

                // Add to current filters list for sql
                if (!string.IsNullOrEmpty(fieldCondition))
                {
                    currentFiltersSqlConditions.Add(new FieldSqlCondition { fieldName = fieldName, sql = fieldCondition });
                }
            }

            // Sql
            string sql = string.Join(
                // Union each field's results
                " UNION ",
                // Generate list of options results for each field
                filterData.fields.Where(f => !f.isDate).Select(field => {
                    List<FilterOption> options = filterData.filterOptions[field.field].Where(option => option.name.ToLower() != "any" && option.name.ToLower() != "not specified").ToList();
                    List<FieldSqlCondition> conditions = currentFiltersSqlConditions.Where(c => c.fieldName != field.field).ToList();

                    return $@"
                        {(options.Count() > 0
                            ? $@"
                                SELECT '{field.field}'             [FieldName]
                                        ,CAST([option] AS VARCHAR) [OptionName]
                                        ,(
                                            SELECT COUNT([ServiceTemplateId])
                                            FROM [dbo].[RSS_ServiceTemplateView]
                                            WHERE {(string.Join(" AND ", conditions.Select(c => c.sql)))} AND [{field.field}] = [option]
                                        ) [Count]
                                FROM (VALUES {string.Join(",", options.Select(option => field.isDecimal ? $"({option.name})" : $"('{option.name}')"))}) [options] ([option])
                                UNION
                            "
                            : "")}
                        SELECT '{field.field}' [FieldName]
                               ,'Any'          [OptionName]
                               ,COUNT([ServiceTemplateId]) [Count]
                        FROM [dbo].[RSS_ServiceTemplateView]
                        WHERE {(string.Join(" AND ", conditions.Select(c => c.sql)))}
                        UNION
                        SELECT '{field.field}'  [FieldName]
                               ,'Not Specified' [OptionName]
                               ,COUNT([ServiceTemplateId])  [Count]
                        FROM [dbo].[RSS_ServiceTemplateView]
                        WHERE {(string.Join(" AND ", conditions.Select(c => c.sql)))} AND [{field.field}] IS NULL
                    ";
                })
            );

            var cmd = new SqlCommand(sql, _connection);

            try
            {
                using (var sqlReader = await cmd.ExecuteReaderAsync())
                {
                    // Read each option in each field
                    while (sqlReader.Read())
                    {
                        string fieldName = (string)sqlReader["FieldName"];
                        string optionName = (string)sqlReader["OptionName"];
                        int count = (int)sqlReader["Count"];
                        Dictionary<string, int> fieldCountData = null;
                        if (filterCountData.TryGetValue(fieldName, out fieldCountData))
                            fieldCountData[optionName] = count;
                        else
                            filterCountData[fieldName] = new Dictionary<string, int> { { optionName, count } };
                    }
                    sqlReader.Close();
                }

                // Save to cache
                CacheHandler.SaveToCache(CacheHandler.DataType_MultiFilterCountData, cacheKey, filterCountData);

                // If no filters applied, also save to the general cache
                if (!anyFiltersApplied)
                {
                    string generalCacheKey = "ServiceFilterCountData";
                    CacheHandler.SaveToCache(CacheHandler.DataType_MultiFilterCountData, generalCacheKey, filterCountData);
                }

                // Make sure next call uses cache
                if (bypasseCache)
                {
                    SystemParameter.UpdateIntParm("BypassFilterCountDataCacheAdmin", 0);
                }

                return filterCountData;
            }
            catch (Exception e)
            {
                throw e;
            }
        }

        // Helper method to generate a cache key based on the applied filters
        private string GenerateServiceFilterCacheKey(ServiceFilterDataDto filterData, bool anyFiltersApplied)
        {
            string baseKey = "ServiceFilterCountData";

            // If no filters are applied, return the base key
            if (!anyFiltersApplied || filterData.appliedFilters == null)
                return baseKey;

            // Create a hash of the applied filters
            var filterHash = JsonConvert.SerializeObject(filterData.appliedFilters).GetHashCode().ToString("X");
            return $"{baseKey}_{filterHash}";
        }

        private List<FieldSqlCondition> GetSqlConditions()
        {
            List<FieldSqlCondition> sqlConditions = new List<FieldSqlCondition> {
                new FieldSqlCondition { fieldName = "deleted", sql = "[Deleted] = 0" }
            };

            return sqlConditions;
        }

        // Clear Service Database multi-filter cache
        private void ClearServiceFilterCache()
        {
            try
            {
                // Clear all Service-related multi-filter caches
                CacheHandler.DeleteCacheLikeItems(CacheHandler.DataType_MultiFilterOptions, "ServiceMultiFilterOptions_");
                CacheHandler.DeleteCacheLikeItems(CacheHandler.DataType_MultiFilterCountData, "ServiceFilterCountData");
            }
            catch (Exception ex)
            {
                // Log error but don't throw - cache clearing shouldn't break the main operation
                System.Diagnostics.Debug.WriteLine($"Error clearing Service filter cache: {ex.Message}");
            }
        }

    }
}
